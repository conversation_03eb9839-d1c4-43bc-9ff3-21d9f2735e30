#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/@puppeteer+browsers@2.10.5/node_modules/@puppeteer/browsers/lib/cjs/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/@puppeteer+browsers@2.10.5/node_modules/@puppeteer/browsers/lib/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/@puppeteer+browsers@2.10.5/node_modules/@puppeteer/browsers/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/@puppeteer+browsers@2.10.5/node_modules/@puppeteer/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/@puppeteer+browsers@2.10.5/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/@puppeteer+browsers@2.10.5/node_modules/@puppeteer/browsers/lib/cjs/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/@puppeteer+browsers@2.10.5/node_modules/@puppeteer/browsers/lib/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/@puppeteer+browsers@2.10.5/node_modules/@puppeteer/browsers/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/@puppeteer+browsers@2.10.5/node_modules/@puppeteer/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/@puppeteer+browsers@2.10.5/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../lib/cjs/main-cli.js" "$@"
else
  exec node  "$basedir/../../lib/cjs/main-cli.js" "$@"
fi
