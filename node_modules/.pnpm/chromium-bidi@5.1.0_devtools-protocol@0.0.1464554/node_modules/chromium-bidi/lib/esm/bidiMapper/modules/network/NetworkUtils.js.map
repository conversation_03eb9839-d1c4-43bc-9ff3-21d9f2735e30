{"version": 3, "file": "NetworkUtils.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/network/NetworkUtils.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;GAgBG;AAOH,OAAO,EAAC,wBAAwB,EAAC,MAAM,oCAAoC,CAAC;AAE5E,OAAO,EAAC,cAAc,EAAC,MAAM,0BAA0B,CAAC;AAExD,MAAM,UAAU,kBAAkB,CAAC,OAAyB;IAC1D,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;QACpD,OAAO,GAAG,GAAG,GAAG,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC;IAC3D,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC;AACzD,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,GAAW;IACxC,OAAO,kBAAkB,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3D,CAAC;AAED,SAAS,kBAAkB,CAAC,UAAsB;IAChD,4DAA4D;IAC5D,iGAAiG;IACjG,MAAM,SAAS,GAAG,KAAK,CAAC;IACxB,MAAM,MAAM,GAAG,EAAE,CAAC;IAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;QACtD,MAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;QACpD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,KAA4B,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACrC,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC;AAC5B,CAAC;AAED,wEAAwE;AACxE,MAAM,UAAU,uCAAuC,CACrD,OAAkC;IAElC,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;QACrD,IAAI;QACJ,KAAK,EAAE;YACL,IAAI,EAAE,QAAQ;YACd,KAAK;SACN;KACF,CAAC,CAAC,CAAC;AACN,CAAC;AAED,sEAAsE;AACtE,MAAM,UAAU,8CAA8C,CAC5D,OAAsC;IAEtC,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,EAAC,IAAI,EAAE,KAAK,EAAC,EAAE,EAAE,CAAC,CAAC;QACrC,IAAI;QACJ,KAAK,EAAE;YACL,IAAI,EAAE,QAAQ;YACd,KAAK;SACN;KACF,CAAC,CAAC,CAAC;AACN,CAAC;AAED,wEAAwE;AACxE,MAAM,UAAU,uCAAuC,CACrD,OAA0B;IAE1B,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QAC1B,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;QACvC,8CAA8C;QAC9C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;QACzC,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,EAA8B,CAAC,CAAC;AACrC,CAAC;AAED,6EAA6E;AAC7E,MAAM,UAAU,qCAAqC,CACnD,OAAsC;IAEtC,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,EAAC,IAAI,EAAE,KAAK,EAAC,EAAE,EAAE,CAAC,CAAC;QACrC,IAAI;QACJ,KAAK,EAAE;YACL,IAAI,EAAE,QAAQ;YACd,KAAK;SACN;KACF,CAAC,CAAC,CAAC;AACN,CAAC;AAED,6EAA6E;AAC7E,MAAM,UAAU,qCAAqC,CACnD,OAA0B;IAE1B,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QAC1B,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,EAAC,IAAI,EAAE,KAAK,EAAC,EAAE,EAAE,CAAC,CAAC;QACrC,IAAI;QACJ,KAAK,EAAE,KAAK,CAAC,KAAK;KACnB,CAAC,CAAC,CAAC;AACN,CAAC;AAED,MAAM,UAAU,8BAA8B,CAC5C,OAAgC;IAEhC,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QAC1B,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;QACjD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACd,GAAG,IAAI,GAAG,CAAC;QACb,CAAC;QACD,MAAM,WAAW,GACf,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ;YAC3B,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;YACzB,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;QACxB,GAAG,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,WAAW,EAAE,CAAC;QAEtC,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE;YACL,IAAI,EAAE,QAAQ;YACd,KAAK;SACN;KACF,CAAC;AACJ,CAAC;AAED,qEAAqE;AACrE,MAAM,UAAU,0DAA0D,CACxE,MAAmD;IAEnD,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,SAAS;YACZ,OAAO,SAAS,CAAC;QACnB,KAAK,QAAQ;YACX,OAAO,YAAY,CAAC;QACtB,KAAK,oBAAoB;YACvB,OAAO,oBAAoB,CAAC;IAChC,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAC7B,MAA+B;IAE/B,MAAM,MAAM,GAAmB;QAC7B,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAC;QAC5C,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,QAAQ,EACN,MAAM,CAAC,QAAQ,KAAK,SAAS;YAC3B,CAAC;YACD,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC;QACxC,GAAG,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,EAAC,MAAM,EAAE,MAAM,CAAC,OAAO,EAAC,CAAC,CAAC,CAAC,SAAS,CAAC;KAChE,CAAC;IAEF,8DAA8D;IAC9D,MAAM,CAAC,cAAc,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC;IACxC,MAAM,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;IAC1C,MAAM,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC;IAC5C,MAAM,CAAC,mBAAmB,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC;IAClD,MAAM,CAAC,iBAAiB,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC;IAC9C,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;QACtC,MAAM,CAAC,mBAAmB,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC;IACpD,CAAC;IACD,IAAI,MAAM,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;QAC5C,MAAM,CAAC,yBAAyB,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC;IAChE,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,oBAAoB,CAAC,KAAyB;IAC5D,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC5B,OAAO,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IACD,OAAO,KAAK,CAAC,KAAK,CAAC;AACrB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAC7B,MAAmC,EACnC,YAAkC;IAElC,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACpE,MAAM,MAAM,GAAiC;QAC3C,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI;QACxB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM;QAC5B,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,GAAG;QAC/B,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,IAAI,KAAK;QACrC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,QAAQ,IAAI,KAAK;QACzC,GAAG,CAAC,YAAY,CAAC,YAAY,KAAK,SAAS,IAAI;YAC7C,YAAY,EAAE;gBACZ,oBAAoB,EAAE,KAAK;gBAC3B,4EAA4E;gBAC5E,YAAY,EAAE,YAAY,CAAC,YAAY;aACxC;SACF,CAAC;QACF,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI;YACxC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM;SAC9B,CAAC;QACF,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI;YAC1C,QAAQ,EAAE,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;SACpD,CAAC;KACH,CAAC;IAEF,8DAA8D;IAC9D,IAAI,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE,CAAC;QAC5C,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IACD,IAAI,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,SAAS,EAAE,CAAC;QACjD,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;IACnD,CAAC;IACD,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,SAAS,EAAE,CAAC;QAClD,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACrD,CAAC;IACD,IAAI,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAK,SAAS,EAAE,CAAC;QACrD,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;IAC3D,CAAC;IACD,IAAI,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,SAAS,EAAE,CAAC;QACnD,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;IACvD,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,iBAAiB,CACxB,QAAyC;IAEzC,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,QAAQ;YACX,8CAA+B;QACjC,KAAK,MAAM;YACT,0CAA6B;QAC/B,KAAK,KAAK;YACR,wCAA4B;QAC9B;YACE,qBAAqB;YACrB,6EAA6E;YAC7E,wCAA4B;IAChC,CAAC;AACH,CAAC;AAED,MAAM,UAAU,iBAAiB,CAC/B,QAA0B;IAE1B,QAAQ,QAAQ,EAAE,CAAC;QACjB;YACE,OAAO,QAAQ,CAAC;QAClB;YACE,OAAO,KAAK,CAAC;QACf;YACE,OAAO,MAAM,CAAC;IAClB,CAAC;IACD,MAAM,IAAI,wBAAwB,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;AAC7E,CAAC;AACD;;;;;;;GAOG;AACH,MAAM,UAAU,eAAe,CAAC,QAAgB;IAC9C,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,QAAQ,CAC3D,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAC3B,CAAC;AACJ,CAAC;AAUD,SAAS,SAAS,CAAC,GAAQ;IACzB,OAAO,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACxC,CAAC;AAED,0DAA0D;AAC1D,MAAM,UAAU,eAAe,CAC7B,OAAyB,EACzB,GAAW;IAEX,kEAAkE;IAClE,0DAA0D;IAC1D,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAE/B,IACE,OAAO,CAAC,QAAQ,KAAK,SAAS;QAC9B,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,CAAC,EACzC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IACE,OAAO,CAAC,QAAQ,KAAK,SAAS;QAC9B,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,EACvC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;QAClE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IACE,OAAO,CAAC,QAAQ,KAAK,SAAS;QAC9B,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,EACvC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;QACxE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,UAAU,kCAAkC,CAChD,OAAyC;IAEzC,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;QAC5B,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;IACzC,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,UAAU,SAAS,CACvB,MAA0B,EAC1B,SAAiB,CAAC;IAElB,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,CAAC,CAAC;IACX,CAAC;IACD,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC;QACxC,OAAO,CAAC,CAAC;IACX,CAAC;IAED,OAAO,MAAM,GAAG,MAAM,CAAC;AACzB,CAAC"}