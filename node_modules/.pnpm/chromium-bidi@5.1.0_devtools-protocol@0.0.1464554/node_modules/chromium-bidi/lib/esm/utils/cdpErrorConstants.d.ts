/**
 * Copyright 2023 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/** @see https://crsrc.org/c/third_party/devtools-frontend/src/front_end/core/protocol_client/InspectorBackend.ts */
export declare const enum CdpErrorConstants {
    CONNECTION_CLOSED = -32001,
    DEVTOOLS_STUB = -32015,
    GENERIC_ERROR = -32000
}
