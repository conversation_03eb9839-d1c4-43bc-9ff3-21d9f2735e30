{"version": 3, "file": "BidiParser.js", "sourceRoot": "", "sources": ["../../../src/bidiTab/BidiParser.ts"], "names": [], "mappings": "AA+BA,OAAO,KAAK,MAAM,MAAM,uCAAuC,CAAC;AAEhE,MAAM,OAAO,UAAU;IACrB,mBAAmB;IACnB,8BAA8B;IAC9B,gCAAgC,CAC9B,MAAe;QAEf,OAAO,MAAM,CAAC,SAAS,CAAC,gCAAgC,CAAC,MAAM,CAAC,CAAC;IACnE,CAAC;IACD,oCAAoC,CAClC,MAAe;QAEf,OAAO,MAAM,CAAC,SAAS,CAAC,oCAAoC,CAAC,MAAM,CAAC,CAAC;IACvE,CAAC;IACD,8BAA8B,CAC5B,MAAe;QAEf,OAAO,MAAM,CAAC,SAAS,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;IAC7D,CAAC;IACD,oCAAoC,CAClC,MAAe;QAEf,OAAO,MAAM,CAAC,SAAS,CAAC,gCAAgC,CAAC,MAAM,CAAC,CAAC;IACnE,CAAC;IACD,6CAA6C,CAC3C,MAAe;QAEf,OAAO,MAAM,CAAC,SAAS,CAAC,yCAAyC,CAAC,MAAM,CAAC,CAAC;IAC5E,CAAC;IACD,6CAA6C,CAC3C,MAAe;QAEf,OAAO,MAAM,CAAC,SAAS,CAAC,yCAAyC,CAAC,MAAM,CAAC,CAAC;IAC5E,CAAC;IACD,kBAAkB;IAElB,iBAAiB;IACjB,8BAA8B;IAC9B,4BAA4B,CAC1B,MAAe;QAEf,OAAO,MAAM,CAAC,OAAO,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;IAC7D,CAAC;IACD,kBAAkB;IAElB,0BAA0B;IAC1B,8BAA8B;IAC9B,mBAAmB,CAAC,MAAe;QACjC,OAAO,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC;IACD,4BAA4B,CAC1B,MAAe;QAEf,OAAO,MAAM,CAAC,eAAe,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;IACrE,CAAC;IACD,gBAAgB,CAAC,MAAe;QAC9B,OAAO,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACzD,CAAC;IACD,iBAAiB,CAAC,MAAe;QAC/B,OAAO,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAC1D,CAAC;IACD,kBAAkB,CAAC,MAAe;QAChC,OAAO,MAAM,CAAC,eAAe,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IAC3D,CAAC;IACD,2BAA2B,CACzB,MAAe;QAEf,OAAO,MAAM,CAAC,eAAe,CAAC,+BAA+B,CAAC,MAAM,CAAC,CAAC;IACxE,CAAC;IACD,sBAAsB,CACpB,MAAe;QAEf,OAAO,MAAM,CAAC,eAAe,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IACD,mBAAmB,CAAC,MAAe;QACjC,OAAO,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC;IACD,gBAAgB,CAAC,MAAe;QAC9B,OAAO,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACzD,CAAC;IACD,iBAAiB,CAAC,MAAe;QAC/B,OAAO,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAC1D,CAAC;IACD,sBAAsB,CACpB,MAAe;QAEf,OAAO,MAAM,CAAC,eAAe,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IACD,0BAA0B,CACxB,MAAe;QAEf,OAAO,MAAM,CAAC,eAAe,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;IACnE,CAAC;IACD,kBAAkB;IAElB,aAAa;IACb,8BAA8B;IAC9B,qBAAqB,CAAC,MAAe;QACnC,OAAO,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;IACnD,CAAC;IACD,uBAAuB,CAAC,MAAe;QACrC,OAAO,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IACD,sBAAsB,CAAC,MAAe;QACpC,OAAO,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;IACpD,CAAC;IACD,kBAAkB;IAElB,mBAAmB;IACnB,8BAA8B;IAC9B,iCAAiC,CAC/B,MAAe;QAEf,OAAO,MAAM,CAAC,SAAS,CAAC,iCAAiC,CAAC,MAAM,CAAC,CAAC;IACpE,CAAC;IACD,kBAAkB;IAElB,eAAe;IACf,8BAA8B;IAC9B,yBAAyB,CAAC,MAAe;QACvC,OAAO,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;IACxD,CAAC;IACD,yBAAyB,CAAC,MAAe;QACvC,OAAO,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;IACxD,CAAC;IACD,mBAAmB,CAAC,MAAe;QACjC,OAAO,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAClD,CAAC;IACD,kBAAkB;IAElB,iBAAiB;IACjB,8BAA8B;IAC9B,uBAAuB,CAAC,MAAe;QACrC,OAAO,MAAM,CAAC,OAAO,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC;IACD,0BAA0B,CACxB,MAAe;QAEf,OAAO,MAAM,CAAC,OAAO,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IACD,2BAA2B,CACzB,MAAe;QAEf,OAAO,MAAM,CAAC,OAAO,CAAC,+BAA+B,CAAC,MAAM,CAAC,CAAC;IAChE,CAAC;IACD,2BAA2B,CACzB,MAAe;QAEf,OAAO,MAAM,CAAC,OAAO,CAAC,+BAA+B,CAAC,MAAM,CAAC,CAAC;IAChE,CAAC;IACD,sBAAsB,CAAC,MAAe;QACpC,OAAO,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;IAC3D,CAAC;IACD,0BAA0B,CACxB,MAAe;QAEf,OAAO,MAAM,CAAC,OAAO,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IACD,0BAA0B,CACxB,MAAe;QAEf,OAAO,MAAM,CAAC,OAAO,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IACD,qBAAqB,CAAC,MAAe;QACnC,OAAO,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;IACtD,CAAC;IACD,kBAAkB;IAElB,qBAAqB;IACrB,8BAA8B;IAC9B,yBAAyB,CACvB,MAAe;QAEf,OAAO,MAAM,CAAC,WAAW,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;IAC9D,CAAC;IACD,kBAAkB;IAElB,gBAAgB;IAChB,8BAA8B;IAC9B,2BAA2B,CACzB,MAAe;QAEf,OAAO,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;IAC3D,CAAC;IACD,uBAAuB,CAAC,MAAe;QACrC,OAAO,MAAM,CAAC,MAAM,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;IACvD,CAAC;IACD,iBAAiB,CAAC,MAAe;QAC/B,OAAO,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;IACD,mBAAmB,CAAC,MAAe;QACjC,OAAO,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IACnD,CAAC;IACD,oBAAoB,CAAC,MAAe;QAClC,OAAO,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IACpD,CAAC;IACD,8BAA8B,CAC5B,MAAe;QAEf,OAAO,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;IAC9D,CAAC;IACD,kBAAkB;IAElB,iBAAiB;IACjB,8BAA8B;IAC9B,oBAAoB,CAAC,MAAe;QAClC,OAAO,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IACD,sBAAsB,CAAC,MAAe;QACpC,OAAO,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;IACvD,CAAC;IACD,kBAAkB;IAElB,iBAAiB;IACjB,8BAA8B;IAC9B,wBAAwB,CAAC,MAAe;QACtC,OAAO,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;IACzD,CAAC;IACD,qBAAqB,CAAC,MAAe;QACnC,OAAO,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;IACtD,CAAC;IACD,oBAAoB,CAAC,MAAe;QAClC,OAAO,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IACD,kBAAkB;IAElB,uBAAuB;IACvB,8BAA8B;IAC9B,kBAAkB,CAAC,MAAe;QAChC,OAAO,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IACD,oBAAoB,CAAC,MAAe;QAClC,OAAO,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IACvD,CAAC;CAEF"}