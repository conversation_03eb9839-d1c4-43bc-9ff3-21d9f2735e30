{"version": 3, "file": "MapperCdpConnection.js", "sourceRoot": "", "sources": ["../../../src/bidiServer/MapperCdpConnection.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,KAAsB,MAAM,OAAO,CAAC;AAO3C,OAAO,EAAC,eAAe,EAAC,MAAM,sBAAsB,CAAC;AAErD,MAAM,aAAa,GAAG,KAAK,CAAC,sBAAsB,CAAC,CAAC;AACpD,MAAM,SAAS,GAAG,KAAK,CAAC,kBAAkB,CAAC,CAAC;AAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,0BAA0B,CAAC,CAAC;AACtD,6BAA6B;AAC7B,MAAM,OAAO,GAAG,IAAI,GAAG,EAAoB,CAAC;AAC5C,MAAM,SAAS,GAAG,CAAC,IAAe,EAAE,EAAE;IACpC,MAAM,MAAM,GAAG,eAAe,IAAI,EAAE,CAAC;IACrC,IAAI,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACjC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC9B,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,OAAO,yBAAyB;IACpC,cAAc,CAAsB;IACpC,YAAY,CAAkB;IAE9B,MAAM,CAAC,KAAK,CAAC,MAAM,CACjB,aAAkC,EAClC,eAAuB,EACvB,OAAgB;QAEhB,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,CACxC,aAAa,EACb,eAAe,EACf,OAAO,CACR,CAAC;YACF,OAAO,IAAI,yBAAyB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,aAAa,CAAC,KAAK,EAAE,CAAC;YACtB,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED,YACE,aAAkC,EAClC,WAA4B;QAE5B,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;IAClC,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,YAAY,CACvB,eAAgC,EAChC,OAAe;QAEf,IAAI,CAAC;YACH,MAAM,eAAe,CAAC,WAAW,CAAC,kBAAkB,EAAE;gBACpD,UAAU,EAAE,iBAAiB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG;aACxD,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,aAAa,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,KAAK;QACH,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,MAAM,CAAC,gBAAgB,GAAG,CACxB,MAA2C,EAC3C,WAA4B,EAC5B,EAAE;QACF,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;YACvC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;YAC9C,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,CAAC,eAAe,GAAG,CAAC,IAAY,EAAE,EAAE;QACxC,IAAI,CAAC;YACH,MAAM,GAAG,GAGL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAErB,IAAI,GAAG,CAAC,OAAO,KAAK,SAAS,IAAI,GAAG,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAC5D,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACtC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACvB,CAAC;QACH,CAAC;QAAC,MAAM,CAAC;YACP,0CAA0C;YAC1C,WAAW,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,CAAC,mBAAmB,GAAG,CAC3B,MAA8C,EAC9C,EAAE;QACF,SAAS,CACP,yBAAyB,EACzB,MAAM,CAAC,IAAI,EACX,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CACpC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,CAAC,yBAAyB,GAAG,CACjC,MAA6C,EAC7C,EAAE;QACF,SAAS,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;IACxC,CAAC,CAAC;IAEF,MAAM,CAAC,KAAK,CAAC,WAAW,CACtB,aAAkC,EAClC,eAAuB,EACvB,OAAgB;QAEhB,aAAa,CAAC,sBAAsB,CAAC,CAAC;QAEtC,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,oBAAoB,EAAE,CAAC;QAEjE,MAAM,EAAC,QAAQ,EAAE,cAAc,EAAC,GAAG,MAAM,aAAa,CAAC,WAAW,CAChE,qBAAqB,EACrB;YACE,GAAG,EAAE,2BAA2B;YAChC,MAAM,EAAE,CAAC,OAAO;YAChB,UAAU,EAAE,IAAI;SACV,CACT,CAAC;QAEF,MAAM,EAAC,SAAS,EAAE,eAAe,EAAC,GAAG,MAAM,aAAa,CAAC,WAAW,CAClE,uBAAuB,EACvB,EAAC,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,IAAI,EAAC,CAC1C,CAAC;QAEF,MAAM,eAAe,GAAG,aAAa,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;QAEpE,MAAM,WAAW,GAAG,IAAI,eAAe,CACrC,KAAK,EAAE,OAAO,EAAE,EAAE,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC,CACrE,CAAC;QAEF,yCAAyC;QACzC,eAAe,CAAC,EAAE,CAAC,uBAAuB,EAAE,CAAC,MAAM,EAAE,EAAE,CACrD,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAW,CAAC,CAC3C,CAAC;QACF,gDAAgD;QAChD,eAAe,CAAC,EAAE,CAAC,0BAA0B,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACzE,4CAA4C;QAC5C,eAAe,CAAC,EAAE,CAChB,yBAAyB,EACzB,IAAI,CAAC,yBAAyB,CAC/B,CAAC;QAEF,MAAM,eAAe,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;QAEpD,MAAM,aAAa,CAAC,WAAW,CAAC,+BAA+B,EAAE;YAC/D,WAAW,EAAE,KAAK;YAClB,QAAQ,EAAE,cAAc;YACxB,kBAAkB,EAAE,IAAI;SACzB,CAAC,CAAC;QAEH,MAAM,eAAe,CAAC,WAAW,CAAC,oBAAoB,EAAE;YACtD,IAAI,EAAE,kBAAkB;SACzB,CAAC,CAAC;QAEH,IAAI,OAAO,EAAE,CAAC;YACZ,8CAA8C;YAC9C,MAAM,eAAe,CAAC,WAAW,CAAC,oBAAoB,EAAE;gBACtD,IAAI,EAAE,kBAAkB;aACzB,CAAC,CAAC;QACL,CAAC;QAED,0CAA0C;QAC1C,MAAM,eAAe,CAAC,WAAW,CAAC,kBAAkB,EAAE;YACpD,UAAU,EAAE,eAAe;SAC5B,CAAC,CAAC;QAEH,mDAAmD;QACnD,MAAM,eAAe,CAAC,WAAW,CAAC,kBAAkB,EAAE;YACpD,UAAU,EAAE,6BAA6B,cAAc,IAAI;YAC3D,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;QAEH,aAAa,CAAC,qBAAqB,CAAC,CAAC;QACrC,OAAO,WAAW,CAAC;IACrB,CAAC"}