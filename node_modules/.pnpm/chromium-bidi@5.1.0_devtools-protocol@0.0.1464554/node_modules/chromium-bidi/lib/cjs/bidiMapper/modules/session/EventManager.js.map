{"version": 3, "file": "EventManager.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/session/EventManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;;AAGH,+DAKuC;AACvC,wDAAgD;AAChD,gEAAwD;AACxD,oEAA4D;AAC5D,8DAAsD;AAEtD,iEAAyD;AAIzD,2CAAiD;AACjD,qEAIkC;AAElC,MAAM,YAAY;IACP,UAAU,GAAG,IAAI,wBAAS,EAAE,CAAC;IAC7B,UAAU,CAAyC;IACnD,MAAM,CAAsC;IAErD,YACE,KAA0C,EAC1C,SAAiD;QAEjD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;IAC5B,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF;AAYD;;GAEG;AACH,MAAM,iBAAiB,GAAiD,IAAI,GAAG,CAC7E,CAAC,CAAC,0BAAY,CAAC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC,CACnD,CAAC;AAUF,MAAa,YAAa,SAAQ,8BAAmC;IACnE;;;;OAIG;IACH,mBAAmB,GAAG,IAAI,0BAAU,CAGlC,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;IACnB;;;OAGG;IACH,aAAa,GAAG,IAAI,GAAG,EAAgC,CAAC;IACxD;;;;OAIG;IACH,gBAAgB,GAAG,IAAI,GAAG,EAAsC,CAAC;IACjE,oBAAoB,CAAsB;IAC1C,uBAAuB,CAAyB;IAChD;;OAEG;IACH,eAAe,CAGb;IAEF,mBAAmB,CAAqB;IAExC,YACE,sBAA8C,EAC9C,kBAAsC;QAEtC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,oBAAoB,GAAG,IAAI,4CAAmB,CAAC,sBAAsB,CAAC,CAAC;QAC5E,IAAI,CAAC,eAAe,GAAG,IAAI,0BAAU,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CACf,SAAkC,EAClC,eAAuD;QAEvD,OAAO,IAAI,CAAC,SAAS,CAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB,CACd,KAA8B,EAC9B,IAAmE;QAEnE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED,aAAa,CACX,KAAyB,EACzB,SAA0C;QAE1C,IAAI,CAAC,oBAAoB,CACvB,OAAO,CAAC,OAAO,CAAC;YACd,IAAI,EAAE,SAAS;YACf,KAAK,EAAE,KAAK;SACb,CAAC,EACF,SAAS,EACT,KAAK,CAAC,MAAM,CACb,CAAC;IACJ,CAAC;IAED,mBAAmB,CAAC,KAAyB;QAC3C,IAAI,CAAC,0BAA0B,CAC7B,OAAO,CAAC,OAAO,CAAC;YACd,IAAI,EAAE,SAAS;YACf,KAAK,EAAE,KAAK;SACb,CAAC,EACF,KAAK,CAAC,MAAM,CACb,CAAC;IACJ,CAAC;IAED,oBAAoB,CAClB,KAA0C,EAC1C,SAA0C,EAC1C,SAAkC;QAElC,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QACxD,MAAM,kBAAkB,GACtB,IAAI,CAAC,oBAAoB,CAAC,gCAAgC,CACxD,SAAS,EACT,SAAS,CACV,CAAC;QACJ,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAC3C,wDAAwD;QACxD,KAAK,MAAM,WAAW,IAAI,kBAAkB,EAAE,CAAC;YAC7C,IAAI,CAAC,IAAI,yCAA2B;gBAClC,OAAO,EAAE,oCAAe,CAAC,iBAAiB,CAAC,KAAK,EAAE,WAAW,CAAC;gBAC9D,KAAK,EAAE,SAAS;aACjB,CAAC,CAAC;YACH,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED,0BAA0B,CACxB,KAA0C,EAC1C,SAAkC;QAElC,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACnD,MAAM,kBAAkB,GACtB,IAAI,CAAC,oBAAoB,CAAC,wCAAwC,CAChE,SAAS,CACV,CAAC;QACJ,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAC3C,6DAA6D;QAC7D,KAAK,MAAM,WAAW,IAAI,kBAAkB,EAAE,CAAC;YAC7C,IAAI,CAAC,IAAI,yCAA2B;gBAClC,OAAO,EAAE,oCAAe,CAAC,iBAAiB,CAAC,KAAK,EAAE,WAAW,CAAC;gBAC9D,KAAK,EAAE,SAAS;aACjB,CAAC,CAAC;YACH,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CACb,UAAqC,EACrC,UAA6C,EAC7C,cAAqC,EACrC,WAAwB;QAExB,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC9B,IAAA,gCAAoB,EAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,cAAc,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;YAC/C,MAAM,IAAI,sCAAwB,CAChC,qDAAqD,CACtD,CAAC;QACJ,CAAC;QAED,6CAA6C;QAC7C,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAE5D,0BAA0B;QAC1B,MAAM,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;QAEvE,MAAM,kBAAkB,GAAG,IAAI,GAAG,CAAC,IAAA,qCAAY,EAAC,UAAU,CAAC,CAAC,CAAC;QAC7D,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAwC,CAAC;QAC5E,MAAM,wBAAwB,GAAG,IAAI,GAAG,CACtC,UAAU,CAAC,MAAM;YACf,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;gBAC3B,MAAM,EAAE,GACN,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;gBAChE,IAAI,CAAC,EAAE,EAAE,CAAC;oBACR,MAAM,IAAI,sCAAwB,CAAC,oBAAoB,CAAC,CAAC;gBAC3D,CAAC;gBACD,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC;YACJ,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CACxE,CAAC;QAEF,KAAK,MAAM,SAAS,IAAI,kBAAkB,EAAE,CAAC;YAC3C,MAAM,sBAAsB,GAAG,IAAI,GAAG,CACpC,IAAI,CAAC,uBAAuB;iBACzB,mBAAmB,EAAE;iBACrB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;iBAChB,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE;gBACb,OAAO,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YACjE,CAAC,CAAC,CACL,CAAC;YACF,mBAAmB,CAAC,GAAG,CACrB,SAAS,EACT,IAAA,mCAAU,EAAC,wBAAwB,EAAE,sBAAsB,CAAC,CAC7D,CAAC;QACJ,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CACtD,UAAU,EACV,UAAU,EACV,cAAc,EACd,WAAW,CACZ,CAAC;QAEF,KAAK,MAAM,SAAS,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;YAChD,KAAK,MAAM,SAAS,IAAI,wBAAwB,EAAE,CAAC;gBACjD,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,kBAAkB,CAChD,SAAS,EACT,SAAS,EACT,WAAW,CACZ,EAAE,CAAC;oBACF,wCAAwC;oBACxC,IAAI,CAAC,IAAI,yCAA2B;wBAClC,OAAO,EAAE,oCAAe,CAAC,iBAAiB,CACxC,YAAY,CAAC,KAAK,EAClB,WAAW,CACZ;wBACD,KAAK,EAAE,SAAS;qBACjB,CAAC,CAAC;oBACH,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;gBAC5D,CAAC;YACH,CAAC;QACH,CAAC;QAED,KAAK,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,IAAI,mBAAmB,EAAE,CAAC;YAC1D,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;gBACnC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAED,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAEnC,OAAO,YAAY,CAAC,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,WAAW,CACf,UAAqC,EACrC,UAA6C,EAC7C,WAAwB;QAExB,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC9B,IAAA,gCAAoB,EAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;QAC3E,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,eAAyB;QAC9C,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;QAC3D,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,qBAAqB;QACzB,2CAA2C;QAC3C,qCAAqC;QACrC,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAClE,OAAO,MAAM,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAC/C,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,mBAAmB,CAAC,SAAiB;QACnC,KAAK,MAAM,SAAS,IAAI,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC;YACjD,MAAM,YAAY,GAAG,EAAY,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAEnE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,YAA0B,EAAE,SAAkC;QACzE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACtC,6CAA6C;YAC7C,OAAO;QACT,CAAC;QACD,MAAM,YAAY,GAAG,EAAY,CAAC,UAAU,CAC1C,SAAS,EACT,YAAY,CAAC,SAAS,CACvB,CAAC;QACF,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,aAAa,CAAC,GAAG,CACpB,YAAY,EACZ,IAAI,kBAAM,CAAe,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC,CAC5D,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACxD,qEAAqE;QACrE,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,cAAc,CACZ,YAA0B,EAC1B,WAAwB,EACxB,SAAkC;QAElC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACtC,6CAA6C;YAC7C,OAAO;QACT,CAAC;QAED,MAAM,cAAc,GAAG,EAAY,CAAC,UAAU,CAC5C,SAAS,EACT,YAAY,CAAC,SAAS,CACvB,CAAC;QAEF,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CACrB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,EAChE,YAAY,CAAC,EAAE,CAChB,CAAC;QAEF,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QACjE,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,gBAAgB,CAAC,GAAG,CACvB,cAAc,EACd,IAAI,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,CAChB,SAAkC,EAClC,SAAiD,EACjD,WAAwB;QAExB,MAAM,YAAY,GAAG,EAAY,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACnE,MAAM,iBAAiB,GACrB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;QAEzE,MAAM,MAAM,GACV,IAAI,CAAC,aAAa;aACf,GAAG,CAAC,YAAY,CAAC;YAClB,EAAE,GAAG,EAAE;aACN,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,GAAG,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAE/D,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACvB,iFAAiF;YACjF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC;iBACvD,MAAM,CACL,CAAC,UAAU,EAAE,EAAE;YACb,oDAAoD;YACpD,UAAU,KAAK,IAAI;gBACnB,mDAAmD;gBACnD,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,UAAU,CAAC,CACtD;iBACA,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAClB,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAC5D;iBACA,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAChD,CAAC;CACF;AA5VD,oCA4VC"}