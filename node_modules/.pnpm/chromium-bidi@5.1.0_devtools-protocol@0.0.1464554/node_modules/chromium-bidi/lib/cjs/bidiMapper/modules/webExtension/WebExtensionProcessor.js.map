{"version": 3, "file": "WebExtensionProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/webExtension/WebExtensionProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAGH,+DAMuC;AAEvC;;GAEG;AACH,MAAa,qBAAqB;IACvB,iBAAiB,CAAY;IAEtC,YAAY,gBAA2B;QACrC,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,OAAO,CACX,MAAsC;QAEtC,QAAQ,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YAClC,KAAK,aAAa,CAAC;YACnB,KAAK,QAAQ;gBACX,MAAM,IAAI,2CAA6B,CACrC,kDAAkD,CACnD,CAAC;YACJ,KAAK,MAAM;gBACT,MAAM;QACV,CAAC;QACD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACvD,yBAAyB,EACzB;gBACE,IAAI,EAAE,MAAM,CAAC,aAAa,CAAC,IAAI;aAChC,CACF,CAAC;YACF,OAAO;gBACL,SAAS,EAAE,QAAQ,CAAC,EAAE;aACvB,CAAC;QACJ,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAK,GAAa,CAAC,OAAO,CAAC,UAAU,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBAC/D,MAAM,IAAI,0CAA4B,CAAE,GAAa,CAAC,OAAO,CAAC,CAAC;YACjE,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CACb,MAAwC;QAExC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,sBAAsB,EAAE;gBAC/D,EAAE,EAAE,MAAM,CAAC,SAAS;aACrB,CAAC,CAAC;YACH,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IACG,GAAa,CAAC,OAAO;gBACtB,qDAAqD,EACrD,CAAC;gBACD,MAAM,IAAI,yCAA2B,CAAC,uBAAuB,CAAC,CAAC;YACjE,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;CACF;AAvDD,sDAuDC"}