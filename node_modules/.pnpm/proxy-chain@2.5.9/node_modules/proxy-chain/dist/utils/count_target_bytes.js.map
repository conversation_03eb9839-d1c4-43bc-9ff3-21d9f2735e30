{"version": 3, "file": "count_target_bytes.js", "sourceRoot": "", "sources": ["../../src/utils/count_target_bytes.ts"], "names": [], "mappings": ";;;AAEA,MAAM,kBAAkB,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACxD,MAAM,eAAe,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAClD,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAClC,MAAM,oBAAoB,GAAG,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAiB5D,2EAA2E;AAC3E,gEAAgE;AAChE,SAAS,UAAU,CAAC,MAAe,IAA0C,CAAC;AAEvE,MAAM,gBAAgB,GAAG,CAC5B,MAAkB,EAClB,MAA+B,EAC/B,oBAAoD,EAChD,EAAE;IACN,UAAU,CAAC,MAAM,CAAC,CAAC;IAEnB,MAAM,CAAC,kBAAkB,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC7D,MAAM,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvD,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;IAE/C,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAE5B,MAAM,YAAY,GAAG,GAAG,EAAE;QACtB,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,MAAM,CAAC,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC;QACzF,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,CAAC,CAAC,CAAC;QAChF,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC,CAAC;IACF,IAAI,CAAC,oBAAoB,EAAE;QACvB,oBAAoB,GAAG,CAAC,OAAmB,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KACjF;IACD,oBAAoB,CAAC,YAAY,CAAC,CAAC;IAEnC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE;QAC/B,MAAM,CAAC,oBAAoB,CAAC,GAAG,GAAG,EAAE;YAChC,IAAI,YAAY,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;YAC9C,IAAI,SAAS,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;YAExC,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,EAAE;gBAClC,YAAY,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,MAAM,CAAC,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC3E,SAAS,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,CAAC,CAAC,CAAC;aACrE;YAED,OAAO;gBACH,YAAY;gBACZ,SAAS;aACZ,CAAC;QACN,CAAC,CAAC;KACL;AACL,CAAC,CAAC;AAvCW,QAAA,gBAAgB,oBAuC3B;AAEK,MAAM,cAAc,GAAG,CAAC,MAAkB,EAAS,EAAE;IACxD,UAAU,CAAC,MAAM,CAAC,CAAC;IAEnB,IAAI,MAAM,CAAC,oBAAoB,CAAC,EAAE;QAC9B,OAAO,MAAM,CAAC,oBAAoB,CAAC,EAAE,CAAC;KACzC;IAED,OAAO;QACH,YAAY,EAAE,IAAI;QAClB,SAAS,EAAE,IAAI;KAClB,CAAC;AACN,CAAC,CAAC;AAXW,QAAA,cAAc,kBAWzB"}