{"version": 3, "file": "tcp_tunnel_tools.js", "sourceRoot": "", "sources": ["../src/tcp_tunnel_tools.ts"], "names": [], "mappings": ";;;;AAAA,gEAA2B;AAC3B,uCAA+B;AAE/B,mCAAgC;AAChC,6CAA0C;AAE1C,MAAM,cAAc,GAAyE,EAAE,CAAC;AAEhG,MAAM,UAAU,GAAG,CAAC,MAAkB,EAAE,EAAE;IACtC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,OAAO,EAAqB,CAAC;IAE5E,IAAI,MAAM,KAAK,MAAM,EAAE;QACnB,OAAO,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;KAC9B;IAED,OAAO,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC;AAC7B,CAAC,CAAC;AAEK,KAAK,UAAU,YAAY,CAC9B,QAAgB,EAChB,UAAkB,EAClB,OAGC,EACD,QAAyD;IAEzD,MAAM,cAAc,GAAG,IAAI,cAAG,CAAC,QAAQ,CAAC,CAAC;IACzC,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;QACxD,MAAM,IAAI,KAAK,CAAC,gEAAgE,QAAQ,IAAI,CAAC,CAAC;KACjG;IAED,MAAM,GAAG,GAAG,IAAI,cAAG,CAAC,aAAa,UAAU,IAAI,EAAE,EAAE,CAAC,CAAC;IAErD,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;QACf,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;KAC9C;IAED,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;QACX,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;KAC1C;IAED,MAAM,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC;IAE3C,MAAM,MAAM,GAAwD,kBAAG,CAAC,YAAY,EAAE,CAAC;IAEvF,MAAM,GAAG,GAAG,CAAC,GAAG,IAAe,EAAQ,EAAE;QACrC,IAAI,OAAO;YAAE,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;IACtC,CAAC,CAAC;IAEF,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;IAEjB,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,YAAY,EAAE,EAAE;;QACrC,MAAM,aAAa,GAAG,GAAG,YAAY,CAAC,aAAa,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;QAEjF,MAAM,EAAE,WAAW,EAAE,GAAG,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QAE3D,GAAG,CAAC,8BAA8B,aAAa,EAAE,CAAC,CAAC;QAEnD,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAE;YAClC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAEjC,GAAG,CAAC,mBAAmB,aAAa,qBAAqB,QAAQ,EAAE,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAE9B,IAAA,aAAK,EAAC;YACF,OAAO,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE;YAC5B,YAAY;YACZ,WAAW,EAAE;gBACT,sBAAsB,EAAE,cAAc;gBACtC,8BAA8B,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,sBAAsB,mCAAI,KAAK;aAC3E;YACD,MAAM,EAAE,MAA0C;YAClD,OAAO,EAAE,IAAI;SAChB,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,MAAM,OAAO,GAAG,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACpD,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAE7B,8CAA8C;QAC9C,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE;YAClB,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;YAEnC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC5B,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,GAAG,EAAE,EAAE,CAAC;YAE7D,GAAG,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;YAErC,OAAO,CAAC,OAAO,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,OAAO,IAAA,iBAAO,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACtC,CAAC;AA9ED,oCA8EC;AAEM,KAAK,UAAU,WAAW,CAC7B,UAAkB,EAClB,gBAAqC,EACrC,QAAyD;IAEzD,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAI,cAAG,CAAC,SAAS,UAAU,EAAE,CAAC,CAAC;IAC1D,IAAI,CAAC,QAAQ;QAAE,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACnE,IAAI,CAAC,IAAI;QAAE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;IAE3D,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QACpC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;YAC7B,OAAO,CAAC,KAAK,CAAC,CAAC;YACf,OAAO;SACV;QACD,IAAI,CAAC,gBAAgB,EAAE;YACnB,OAAO,CAAC,IAAI,CAAC,CAAC;YACd,OAAO;SACV;QACD,KAAK,MAAM,UAAU,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE;YAC7D,UAAU,CAAC,OAAO,EAAE,CAAC;SACxB;QACD,OAAO,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC,CAAC;SACG,IAAI,CAAC,KAAK,EAAE,YAAY,EAAE,EAAE,CAAC,IAAI,OAAO,CAAU,CAAC,OAAO,EAAE,EAAE;QAC3D,IAAI,CAAC,YAAY,EAAE;YACf,OAAO,CAAC,KAAK,CAAC,CAAC;YACf,OAAO;SACV;QACD,cAAc,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;YACzC,OAAO,cAAc,CAAC,UAAU,CAAC,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC;IAER,OAAO,IAAA,iBAAO,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACtC,CAAC;AAnCD,kCAmCC"}