{"version": 3, "file": "anonymize_proxy.js", "sourceRoot": "", "sources": ["../src/anonymize_proxy.ts"], "names": [], "mappings": ";;;AAGA,uCAA+B;AAE/B,qCAAmD;AACnD,6CAA0C;AAE1C,qFAAqF;AACrF,MAAM,0BAA0B,GAA2B,EAAE,CAAC;AAQ9D;;;;GAIG;AACI,MAAM,cAAc,GAAG,KAAK,EAC/B,OAAuC,EACvC,QAAwC,EACzB,EAAE;IACjB,IAAI,QAAgB,CAAC;IACrB,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,sBAAsB,GAAG,KAAK,CAAC;IAEnC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC7B,QAAQ,GAAG,OAAO,CAAC;KACtB;SAAM;QACH,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC;QACvB,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAEpB,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,EAAE;YAC1B,MAAM,IAAI,KAAK,CACX,wEAAwE,CAC3E,CAAC;SACL;QAED,IAAI,OAAO,CAAC,sBAAsB,KAAK,SAAS,EAAE;YAC9C,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,CAAC;SAC3D;KACJ;IAED,MAAM,cAAc,GAAG,IAAI,cAAG,CAAC,QAAQ,CAAC,CAAC;IACzC,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,wBAAe,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;QAC5E,MAAM,IAAI,KAAK,CAAC,+FAA+F,wBAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,cAAc,IAAI,CAAC,CAAC;KAChN;IAED,sHAAsH;IACtH,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,CAAC,CAAC,sBAAsB,IAAI,cAAc,CAAC,QAAQ,KAAK,QAAQ,CAAC,EAAE;QAC3H,OAAO,IAAA,iBAAO,EAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC;KACvD;IAED,IAAI,MAAiC,CAAC;IAEtC,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;QAC3B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;YACrC,MAAM,GAAG,IAAI,eAAM,CAAC;gBAChB,iBAAiB;gBACjB,IAAI;gBACJ,IAAI,EAAE,WAAW;gBACjB,sBAAsB,EAAE,GAAG,EAAE;oBACzB,OAAO;wBACH,qBAAqB,EAAE,KAAK;wBAC5B,gBAAgB,EAAE,QAAQ;wBAC1B,8BAA8B,EAAE,sBAAsB;qBACzD,CAAC;gBACN,CAAC;aACJ,CAA8B,CAAC;YAEhC,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IAEF,MAAM,OAAO,GAAG,WAAW,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;QACpC,MAAM,GAAG,GAAG,oBAAoB,MAAM,CAAC,IAAI,EAAE,CAAC;QAC9C,0BAA0B,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;QACzC,OAAO,GAAG,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,OAAO,IAAA,iBAAO,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACtC,CAAC,CAAC;AA/DW,QAAA,cAAc,kBA+DzB;AAEF;;;;;GAKG;AACI,MAAM,oBAAoB,GAAG,KAAK,EACrC,kBAA0B,EAC1B,gBAAyB,EACzB,QAA0D,EAC1C,EAAE;IAClB,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE;QACxC,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;KAC1E;IAED,MAAM,MAAM,GAAG,0BAA0B,CAAC,kBAAkB,CAAC,CAAC;IAC9D,IAAI,CAAC,MAAM,EAAE;QACT,OAAO,IAAA,iBAAO,EAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC;KACpD;IAED,OAAO,0BAA0B,CAAC,kBAAkB,CAAC,CAAC;IAEtD,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;QACrD,OAAO,IAAI,CAAC;IAChB,CAAC,CAAC,CAAC;IACH,OAAO,IAAA,iBAAO,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACtC,CAAC,CAAC;AApBW,QAAA,oBAAoB,wBAoB/B;AAYF;;;;;GAKG;AACI,MAAM,4BAA4B,GAAG,CACxC,kBAA0B,EAC1B,8BAAwC,EACjC,EAAE;IACT,MAAM,MAAM,GAAG,0BAA0B,CAAC,kBAAkB,CAAC,CAAC;IAC9D,IAAI,CAAC,MAAM,EAAE;QACT,OAAO,KAAK,CAAC;KAChB;IACD,MAAM,CAAC,EAAE,CAAC,wBAAwB,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE;QAC/D,8BAA8B,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC;IACH,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAZW,QAAA,4BAA4B,gCAYvC"}