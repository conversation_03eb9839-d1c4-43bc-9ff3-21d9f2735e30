"use strict";

var _puppeteer = _interopRequireDefault(await jitiImport("puppeteer"));
var _proxyChain = _interopRequireDefault(await jitiImport("proxy-chain"));function _interopRequireDefault(e) {return e && e.__esModule ? e : { default: e };} // http://jqzex0xj8xizuo3:<EMAIL>:6060

(async () => {
  // Replace this with your real proxy URL
  const oldProxyUrl =
  'http://jqzex0xj8xizuo3:<EMAIL>:6060';

  // Create an anonymized proxy URL (proxy-chain will handle rotating the port etc.)
  const newProxyUrl = await _proxyChain.default.anonymizeProxy(oldProxyUrl);

  // Launch browser with the anonymized proxy
  const browser = await _puppeteer.default.launch({
    headless: false,
    args: [`--proxy-server=${newProxyUrl}`]
  });

  const page = await browser.newPage();

  // Go to example.com
  await page.goto('http://example.com');

  // Take a screenshot
  await page.screenshot({ path: 'example.png' });

  console.log('Screenshot saved as example.png');

  // Close the browser
  await browser.close();

  // Close the anonymized proxy
  //   await closeAnonymizedProxy(newProxyUrl, true);
})(); /* v9-4b9f680e5fa0c83b */
