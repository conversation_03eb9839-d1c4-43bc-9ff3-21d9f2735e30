#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/puppeteer@24.11.1/node_modules/puppeteer/lib/cjs/puppeteer/node/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/puppeteer@24.11.1/node_modules/puppeteer/lib/cjs/puppeteer/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/puppeteer@24.11.1/node_modules/puppeteer/lib/cjs/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/puppeteer@24.11.1/node_modules/puppeteer/lib/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/puppeteer@24.11.1/node_modules/puppeteer/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/puppeteer@24.11.1/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/puppeteer@24.11.1/node_modules/puppeteer/lib/cjs/puppeteer/node/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/puppeteer@24.11.1/node_modules/puppeteer/lib/cjs/puppeteer/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/puppeteer@24.11.1/node_modules/puppeteer/lib/cjs/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/puppeteer@24.11.1/node_modules/puppeteer/lib/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/puppeteer@24.11.1/node_modules/puppeteer/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/puppeteer@24.11.1/node_modules:/home/<USER>/Desktop/data-automators/projects/exp-proxy-test/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../puppeteer/lib/cjs/puppeteer/node/cli.js" "$@"
else
  exec node  "$basedir/../puppeteer/lib/cjs/puppeteer/node/cli.js" "$@"
fi
